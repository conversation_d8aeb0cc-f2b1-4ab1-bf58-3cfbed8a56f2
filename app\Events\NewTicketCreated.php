<?php

namespace App\Events;

use App\Models\Ticket;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class NewTicketCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Ticket $ticket;

    public function __construct(Ticket $ticket)
    {
        $this->ticket = $ticket;
    }

    public function broadcastOn(): Channel
    {
        // Broadcast đến kênh của phòng ban
        return new Channel('department.'.$this->ticket->department_id);
    }

    public function broadcastAs(): string
    {
        return 'new-ticket-created';
    }

    public function broadcastWith(): array
    {
        return [
            'ticket' => $this->ticket->load('user', 'categories', 'tags'),
        ];
    }
}
