<?php

namespace App\Http\Controllers\Ticket;

use App\Http\Controllers\Controller;
use App\Mail\TicketCreated;
use App\Models\Ticket;
use App\Models\User;
use App\Notifications\TicketCreatedNotification;
use App\Services\TicketService;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Inertia\Inertia;

class TicketController extends Controller
{
    use HandlesAuthorization;

    protected TicketService $ticketService;

    public function __construct(TicketService $ticketService)
    {
        $this->ticketService = $ticketService;
    }

    /**
     * Display a listing of tickets
     */
    public function index(Request $request): \Inertia\Response
    {
        $data = $this->ticketService->getTicketsForIndex($request);

        // Add search suggestions
        $data['searchSuggestions'] = $this->getSearchSuggestions();

        return Inertia::render('Ticket/Index', $data);
    }

    /**
     * Get tickets data for AJAX requests
     */
    public function getTicketsData(Request $request): \Illuminate\Http\JsonResponse
    {
        $data = $this->ticketService->getTicketsForIndex($request);
        return response()->json(['props' => $data]);
    }

    /**
     * Display ticket manager with SPA-like navigation
     */
    // public function manager(Request $request): \Inertia\Response
    // {
    //     // Get all tickets data
    //     $allTicketsData = $this->ticketService->getTicketsForIndex($request);

    //     // Get my tickets count
    //     $myTicketsCount = auth()->check() ?
    //         \App\Models\Post::where('user_id', auth()->id())->count() : 0;

    //     $data = array_merge($allTicketsData, [
    //         'allTicketsCount' => $allTicketsData['ticketCount'],
    //         'myTicketsCount' => $myTicketsCount,
    //         'initialTickets' => $allTicketsData['tickets'],
    //         'initialPagination' => $allTicketsData['pagination'],
    //         'initialFilters' => $allTicketsData['filters'],
    //     ]);

    //     return Inertia::render('Ticket/TicketSPA', $data);
    // }
    
    /**
     * Show the form for creating a new ticket
     */
    public function create(): \Inertia\Response
    {
        $data = $this->ticketService->getCreateTicketData();
        return Inertia::render('Ticket/Create', $data);
    }

    /**
     * Display the specified ticket
     */
    public function show(string $slug): \Inertia\Response
    {
        $ticket = Ticket::where('slug', $slug)
            ->with([
                'user:id,name,profile_photo_path,email',
                'categories:id,title,slug',
                'tags:id,name',
                'assignee:id,name,profile_photo_path,email',
                'department:id,name',
            ])
            ->withCount('upvotes')
            ->firstOrFail();

        // Get formatted comments using the same method as other controllers
        $comments = $ticket->getFormattedComments();

        // Debug comments
        \Log::info('Ticket/TicketController comments debug', [
            'ticket_id' => $ticket->id,
            'comments_count' => $comments->count(),
            'comments_data' => $comments->toArray()
        ]);

        // Check if current user has upvoted this ticket
        $hasUpvote = auth()->check() ? $ticket->isUpvotedBy(auth()->id()) : false;

        $ticketData = [
            'id' => $ticket->id,
            'slug' => $ticket->slug,
            'title' => $ticket->title,
            'is_published' => $ticket->is_published,
            'content' => $ticket->content,
            'status' => $ticket->status,
            'priority' => $ticket->priority,
            'created_at' => $ticket->created_at->diffForHumans(),
            'updated_at' => $ticket->updated_at->format('Y-m-d H:i:s'),
            'user' => [
                'id' => $ticket->user->id,
                'name' => $ticket->user->name,
                'email' => $ticket->user->email,
                'profile_photo_path' => $ticket->user->profile_photo_path,
                'profile_photo_url' => $ticket->user->profile_photo_url,
            ],
            'assignee' => $ticket->assignee ? [
                'id' => $ticket->assignee->id,
                'name' => $ticket->assignee->name,
                'email' => $ticket->assignee->email,
                'profile_photo_path' => $ticket->assignee->profile_photo_path,
                'profile_photo_url' => $ticket->assignee->profile_photo_url,
            ] : null,
            'department' => $ticket->department,
            'categories' => $ticket->categories,
            'tags' => $ticket->tags,
            'comments' => $comments,
            'upvote_count' => $ticket->upvotes_count,
            'has_upvote' => $hasUpvote,
        ];

        $data = $this->ticketService->getCreateTicketData();
        $data['ticket'] = $ticketData;

        return Inertia::render('Ticket/TicketDetail', $data);
    }

    /**
     * Search tickets
     */
    public function search(Request $request): \Inertia\Response
    {
        $startTime = microtime(true);
        $query = $request->input('q', '');

        if (!$query) {
            return Inertia::render('Ticket/SearchPage', [
                'tickets' => [],
                'query' => '',
                'totalResults' => 0,
                'searchTime' => 0,
                'suggestions' => ['bug', 'feature request', 'urgent', 'payment', 'login'],
                'categories' => \App\Models\Category::select(['id', 'title', 'slug'])->get(),
                'tags' => \App\Models\Tag::select(['id', 'name'])->get(),
                'departments' => \App\Models\Department::select(['id', 'name'])->get(),
                'users' => \App\Models\User::select(['id', 'name'])->get(),
                'filters' => [
                    'search' => $query,
                    'status' => $request->input('status'),
                    'priority' => $request->input('priority'),
                    'sortBy' => $request->input('sortBy', 'relevance'),
                ],
                'notifications' => [],
            ]);
        }

        $data = $this->ticketService->searchTickets($request);
        $endTime = microtime(true);
        $searchTime = round(($endTime - $startTime) * 1000); // Convert to milliseconds

        // Add search-specific data
        $data['query'] = $query;
        $data['totalResults'] = $data['tickets']->total();
        $data['searchTime'] = $searchTime;
        $data['suggestions'] = ['bug', 'feature request', 'urgent', 'payment', 'login'];

        return Inertia::render('Ticket/SearchPage', $data);
    }

    /**
     * Get search suggestions from various sources
     */
    private function getSearchSuggestions(): array
    {
        // Get popular search terms from actual ticket data
        $popularTerms = \DB::table('tickets')
            ->select(\DB::raw('LOWER(title) as term'))
            ->where('is_published', true)
            ->whereNotNull('title')
            ->get()
            ->pluck('term')
            ->flatMap(function ($title) {
                // Extract meaningful words from titles
                $words = preg_split('/[\s\-_]+/', $title);
                return array_filter($words, function ($word) {
                    return strlen($word) >= 3 && !in_array(strtolower($word), [
                        'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'why', 'oil', 'sit', 'set'
                    ]);
                });
            })
            ->countBy()
            ->sortDesc()
            ->take(10)
            ->keys()
            ->toArray();

        // Get category names as suggestions
        $categoryTerms = \App\Models\Category::select('title')
            ->get()
            ->pluck('title')
            ->map(fn($title) => strtolower($title))
            ->toArray();

        // Get common status and priority terms
        $systemTerms = [
            'bug', 'issue', 'problem', 'error', 'urgent', 'high priority',
            'feature request', 'enhancement', 'improvement',
            'payment', 'billing', 'account', 'login', 'password',
            'support', 'help', 'question', 'how to'
        ];

        // Combine and deduplicate
        $allSuggestions = array_unique(array_merge($popularTerms, $categoryTerms, $systemTerms));

        // Return top 15 suggestions
        return array_slice($allSuggestions, 0, 15);
    }

    /**
     * API endpoint to get search suggestions
     */
    public function apiSearchSuggestions(Request $request)
    {
        $query = $request->input('q', '');
        $limit = $request->input('limit', 10);

        if (strlen($query) < 2) {
            return response()->json([
                'suggestions' => $this->getSearchSuggestions()
            ]);
        }

        // Get suggestions based on query
        $suggestions = collect($this->getSearchSuggestions())
            ->filter(function ($suggestion) use ($query) {
                return stripos($suggestion, $query) !== false;
            })
            ->take($limit)
            ->values()
            ->toArray();

        // If no matches, get popular terms that contain the query
        if (empty($suggestions)) {
            $suggestions = \DB::table('posts')
                ->select(\DB::raw('LOWER(title) as term'))
                ->where('is_published', true)
                ->where('title', 'LIKE', "%{$query}%")
                ->limit($limit)
                ->get()
                ->pluck('term')
                ->unique()
                ->values()
                ->toArray();
        }

        return response()->json([
            'suggestions' => $suggestions,
            'query' => $query
        ]);
    }

    public function showForm()
    {
        return Inertia::render('Ticket/Form');
    }

    public function submitTicket(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
        ]);
        $user = User::where('email', $validated['email'])->first();

        if (! $user) {
            $user = $this->createGuestUser($validated['email']);
        }

        Auth::login($user);

        $ticket = Ticket::create([
            'user_id' => auth()->id(),
            'title' => $validated['title'],
            'content' => $validated['content'],
            'status' => 'open',
        ]);
        $this->notifyAdmin($ticket);

        return response()->json([
            'message' => 'Ticket created successfully',
            'ticket_id' => $ticket->id,
        ]);

    }

    public function handleWebhook(Request $request)
    {
        $payload = $request->all();

        $ticket = Ticket::create([
            'user_id' => $payload['user_id'] ?? null,
            'title' => $payload['title'],
            'content' => $payload['content'],
            'is_published' => false,
        ]);

        $this->notifyAdmin($ticket);

        return response()->json(['status' => 'success']);
    }

    private function createGuestUser($email)
    {
        return User::create([
            'email' => $email,
            'password' => Hash::make(uniqid()),
            'name' => 'Guest_'.substr(md5($email), 0, 8),
            'is_guest' => true,
        ]);
    }

    private function notifyAdmin($ticket)
    {
        $admins = User::hasRole('admin');
        foreach ($admins as $admin) {
            Mail::to($admin->email)->queue(new TicketCreated($ticket));
        }
    }

    public function store(Request $request): \Illuminate\Http\RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'priority' => 'required|in:low,medium,high,urgent',
            'department_id' => 'required|exists:departments,id',
            'assignee_id' => 'nullable|exists:users,id',
            'categories' => 'nullable|array',
            'categories.*' => 'exists:categories,id',
            'tags' => 'nullable|array',
            'tags.*' => 'exists:tags,id',
        ]);

        $slug = Str::slug($validated['title']);
        $originalSlug = $slug;
        $counter = 1;

        // Ensure unique slug
        while (Ticket::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        $ticket = Ticket::create([
            'title' => $validated['title'],
            'content' => $validated['content'],
            'priority' => $validated['priority'],
            'department_id' => $validated['department_id'],
            'user_id' => auth()->id(),
            'assignee_id' => $validated['assignee_id'] ?? null,
            'slug' => $slug,
            'status' => 'open',
            'is_published' => true,
        ]);

        // Attach categories and tags
        if (!empty($validated['categories'])) {
            $ticket->categories()->attach($validated['categories']);
        }

        if (!empty($validated['tags'])) {
            $ticket->tags()->attach($validated['tags']);
        }

        // Gửi thông báo đến phòng ban
        if ($ticket->department) {
            $ticket->department->users()->each(function ($user) use ($ticket) {
                $user->notify(new TicketCreatedNotification($ticket));
            });
        }

        return redirect()->back()
            ->with('success', 'Ticket created successfully!');
    }

    /**
     * Store ticket from API (existing method for backward compatibility)
     */
    public function storeApi(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'priority' => 'required|in:low,medium,high,urgent',
            'department_id' => 'required|exists:departments,id',
            'user_id' => 'required|exists:users,id',
            'assignee_id' => 'nullable|exists:users,id',
        ]);

        $slug = Str::slug($validated['title']);

        if (Ticket::existsByTitleOrSlug($validated['title'], $slug)) {
            return response()->json(['error' => 'Title or slug already exists'], 422);
        }

        $ticket = Ticket::create([
            'title' => $validated['title'],
            'content' => $validated['content'],
            'priority' => $validated['priority'],
            'department_id' => $validated['department_id'],
            'user_id' => $validated['user_id'],
            'assignee_id' => $validated['assignee_id'],
            'slug' => $slug,
            'status' => 'open',
            'is_published' => true,
        ]);

        // Gửi thông báo đến phòng ban
        $ticket->department->users()->each(function ($user) use ($ticket) {
            $user->notify(new TicketCreatedNotification($ticket));
        });

        return response()->json([
            'message' => 'Ticket created successfully',
            'ticket' => $ticket->toFormattedArray(),
        ], 201);
    }
}
