<?php

namespace App\Notifications;

use App\Models\Ticket;
use Illuminate\Broadcasting\Channel;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewTicket extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public Ticket $ticket;

    public function __construct(Ticket $ticket)
    {
        $this->ticket = $ticket;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->line('The introduction to the notification.')
            ->action('Notification Action', url('/'))
            ->line('Thank you for using our application!');
    }

    public function toArray($notifiable)
    {
        return [
            'ticket_id' => $this->ticket->id,
            'title' => $this->ticket->title,
            'content' => $this->ticket->getExcerpt(),
            'slug' => $this->ticket->slug,
            'message' => "New ticket created: {$this->ticket->title}",
            'name' => $this->ticket->user->name,
            'profile_photo_url' => $this->ticket->user->profile_photo_path
                ? asset('storage/'.$this->ticket->user->profile_photo_path)
                : null,
            'tags' => $this->ticket->tags->pluck('name')->toArray(),
            'categories' => $this->ticket->categories->pluck('title')->toArray(),
            'product_id' => $this->ticket->product_id,
            'product_name' => $this->ticket->product_name,
            'created_at' => $this->ticket->created_at->diffForHumans(),
        ];
    }

    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage([
            'id' => $this->ticket->id,
            'data' => $this->toArray($notifiable),
            'created_at' => now()->diffForHumans(),
            'read_at' => null,
        ]);
    }

    public function broadcastOn()
    {
        return new Channel('tickets');
    }
}
