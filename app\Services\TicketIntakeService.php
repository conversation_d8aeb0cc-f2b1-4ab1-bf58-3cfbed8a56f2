<?php

namespace App\Services;

use App\Models\Category;
use App\Models\Departments;
use App\Models\Tag;
use App\Models\Ticket;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class TicketIntakeService
{
    /**
     * Create a ticket from external system
     */
    public function createFromExternal(User $systemUser, array $data): array
    {
        $sourceSystem = $data['source_system'] ?? $systemUser->system_type ?? 'unknown';

        // Check for duplicate external_id
        if (!empty($data['external_id'])) {
            $existingRef = DB::table('ticket_external_refs')
                ->where('source_system', $sourceSystem)
                ->where('external_id', $data['external_id'])
                ->first();

            if ($existingRef) {
                $existingTicket = Ticket::find($existingRef->ticket_id);
                return [
                    'status' => 'duplicate',
                    'ticket_id' => (int) $existingRef->ticket_id,
                    'ticket' => $existingTicket,
                    'message' => 'Ticket with this external ID already exists',
                ];
            }
        }

        $ticket = null;
        
        DB::transaction(function () use ($systemUser, $data, $sourceSystem, &$ticket) {
            // Resolve category
            $categoryId = $this->resolveCategoryId($data);
            
            // Resolve department
            $departmentId = $this->resolveDepartmentId($data);

            // Generate slug
            $slug = $this->generateUniqueSlug($data['title']);

            // Create the ticket
            $ticket = Ticket::create([
                'title' => $data['title'],
                'content' => $data['content'],
                'user_id' => $systemUser->id, // System User as creator
                'requester_name' => $data['requester_name'],
                'requester_email' => $data['requester_email'],
                'priority' => $data['priority'] ?? 'medium',
                'source_system' => $sourceSystem,
                'external_id' => $data['external_id'] ?? null,
                'category_id' => $categoryId,
                'department_id' => $departmentId,
                'product_id' => $data['product_id'] ?? null,
                'product_name' => $data['product_name'] ?? null,
                'status' => 'open',
                'is_published' => true,
                'slug' => $slug,
                'payload' => $data, // Store original payload for debugging
                'category_type' => $this->determineCategoryType($data, $categoryId),
                'priority_score' => $this->calculatePriorityScore($data),
            ]);

            // Handle tags
            if (!empty($data['tags'])) {
                $this->attachTags($ticket, $data['tags']);
            }

            // Create external reference for duplicate prevention
            if (!empty($data['external_id'])) {
                DB::table('ticket_external_refs')->insert([
                    'ticket_id' => $ticket->id,
                    'source_system' => $sourceSystem,
                    'external_id' => $data['external_id'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Handle attachments if provided
            if (!empty($data['attachments'])) {
                $this->handleAttachments($ticket, $data['attachments']);
            }
        });

        return [
            'status' => 'created',
            'ticket_id' => $ticket->id,
            'ticket' => $ticket->fresh(['creator', 'category', 'department', 'tags']),
            'message' => 'Ticket created successfully',
        ];
    }

    /**
     * Resolve category ID from category_key or category_id
     */
    private function resolveCategoryId(array $data): ?int
    {
        if (!empty($data['category_id'])) {
            return $data['category_id'];
        }

        if (!empty($data['category_key'])) {
            $category = Category::where('slug', $data['category_key'])
                ->orWhere('name', $data['category_key'])
                ->first();
            
            return $category?->id;
        }

        return null;
    }

    /**
     * Resolve department ID
     */
    private function resolveDepartmentId(array $data): ?int
    {
        if (!empty($data['department_id'])) {
            return $data['department_id'];
        }

        // Could add logic to auto-assign department based on category or other criteria
        return null;
    }

    /**
     * Generate unique slug for the ticket
     */
    private function generateUniqueSlug(string $title): string
    {
        $baseSlug = Str::slug($title);
        $slug = $baseSlug;
        $counter = 1;

        while (Ticket::where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Determine category type based on data and category
     */
    private function determineCategoryType(array $data, ?int $categoryId): string
    {
        // If category is provided, use its type
        if ($categoryId) {
            $category = Category::find($categoryId);
            if ($category && isset($category->type)) {
                return $category->type;
            }
        }

        // Determine based on source system or keywords
        $sourceSystem = $data['source_system'] ?? '';
        $title = strtolower($data['title'] ?? '');
        $content = strtolower($data['content'] ?? '');

        if (str_contains($sourceSystem, 'payment') || 
            str_contains($title, 'payment') || 
            str_contains($content, 'payment')) {
            return 'payment';
        }

        if (str_contains($sourceSystem, 'technical') || 
            str_contains($title, 'error') || 
            str_contains($title, 'bug')) {
            return 'technical';
        }

        return 'general';
    }

    /**
     * Calculate priority score (0-100)
     */
    private function calculatePriorityScore(array $data): int
    {
        $priority = $data['priority'] ?? 'medium';
        
        $baseScores = [
            'low' => 25,
            'medium' => 50,
            'high' => 75,
            'urgent' => 90,
        ];

        $score = $baseScores[$priority] ?? 50;

        // Adjust based on keywords in title/content
        $title = strtolower($data['title'] ?? '');
        $content = strtolower($data['content'] ?? '');
        
        $urgentKeywords = ['urgent', 'critical', 'emergency', 'down', 'outage'];
        $highKeywords = ['important', 'asap', 'priority', 'escalate'];
        
        foreach ($urgentKeywords as $keyword) {
            if (str_contains($title, $keyword) || str_contains($content, $keyword)) {
                $score = min(100, $score + 15);
                break;
            }
        }
        
        foreach ($highKeywords as $keyword) {
            if (str_contains($title, $keyword) || str_contains($content, $keyword)) {
                $score = min(100, $score + 10);
                break;
            }
        }

        return $score;
    }

    /**
     * Attach tags to the ticket
     */
    private function attachTags(Ticket $ticket, array $tagNames): void
    {
        $tagIds = [];
        
        foreach ($tagNames as $tagName) {
            $tag = Tag::firstOrCreate(
                ['name' => trim($tagName)],
                ['slug' => Str::slug($tagName)]
            );
            $tagIds[] = $tag->id;
        }

        if (!empty($tagIds)) {
            $ticket->tags()->attach($tagIds);
        }
    }

    /**
     * Handle attachments (store metadata, actual file handling would be separate)
     */
    private function handleAttachments(Ticket $ticket, array $attachments): void
    {
        // This is a placeholder for attachment handling
        // In a real implementation, you might:
        // 1. Download files from provided URLs
        // 2. Store them in your file system
        // 3. Create attachment records in database
        // 4. Scan for viruses
        
        $attachmentData = [];
        foreach ($attachments as $attachment) {
            $attachmentData[] = [
                'name' => $attachment['name'],
                'url' => $attachment['url'],
                'type' => $attachment['type'] ?? 'unknown',
                'processed' => false,
            ];
        }

        // Store attachment metadata in the payload for now
        $payload = $ticket->payload ?? [];
        $payload['attachments'] = $attachmentData;
        $ticket->update(['payload' => $payload]);
    }

    /**
     * Get ticket intake statistics
     */
    public function getIntakeStatistics(array $filters = []): array
    {
        $query = Ticket::whereNotNull('source_system');

        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $total = $query->count();
        
        $bySourceSystem = $query->selectRaw('source_system, COUNT(*) as count')
            ->groupBy('source_system')
            ->orderBy('count', 'desc')
            ->get()
            ->pluck('count', 'source_system')
            ->toArray();

        $byPriority = $query->selectRaw('priority, COUNT(*) as count')
            ->groupBy('priority')
            ->get()
            ->pluck('count', 'priority')
            ->toArray();

        $byStatus = $query->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status')
            ->toArray();

        return [
            'total_tickets' => $total,
            'by_source_system' => $bySourceSystem,
            'by_priority' => $byPriority,
            'by_status' => $byStatus,
        ];
    }
}
