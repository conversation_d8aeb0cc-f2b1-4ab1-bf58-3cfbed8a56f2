<?php

namespace App\Services;

use App\Models\Ticket;
use App\Models\Category;
use App\Models\Tag;
use App\Models\User;
use App\Models\Departments;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class TicketService
{
    /**
     * Number of tickets per page
     */
    const TICKETS_PER_PAGE = 5;

    /**
     * Get tickets for index page with filtering and pagination
     */
    public function getTicketsForIndex(Request $request): array
    {
        $search = $request->input('search', '');
        $sort = $request->input('sort', 'latest');
        $status = $request->input('status', null);
        $priority = $request->input('priority', null);
        $department = $request->input('department', null);
        $assignee = $request->input('assignee', null);
        $category = $request->input('category', null);

        // Build query for tickets
        $query = Ticket::query()
            ->with(['user', 'categories', 'tags', 'assignee', 'department'])
            ->withCount(['upvotes', 'comments']);

        // Apply search
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // Apply filters
        if ($status) {
            $query->where('status', $status);
        }

        if ($priority) {
            $query->where('priority', $priority);
        }

        if ($department) {
            $query->where('department_id', $department);
        }

        if ($assignee) {
            $query->where('assignee_id', $assignee);
        }

        if ($category) {
            $query->whereHas('categories', function ($q) use ($category) {
                // Support both slug and ID for backward compatibility
                if (is_numeric($category)) {
                    $q->where('categories.id', $category);
                } else {
                    $q->where('categories.slug', $category);
                }
            });
        }

        // Apply sorting
        switch ($sort) {
            case 'latest':
            case 'newest':
                $query->latest();
                break;
            case 'oldest':
                $query->oldest();
                break;
            case 'priority':
                $query->orderByRaw("FIELD(priority, 'urgent', 'high', 'medium', 'low')");
                break;
            case 'upvotes':
            case 'most-upvoted':
                $query->orderBy('upvotes_count', 'desc');
                break;
            case 'most-replies':
                $query->orderBy('comments_count', 'desc');
                break;
            case 'inactive':
                // Sort by inactive status first (closed, resolved), then by updated_at desc
                $query->orderByRaw("FIELD(status, 'closed', 'resolved', 'in_progress', 'open')")
                      ->orderBy('updated_at', 'desc');
                break;
        }

        $tickets = $query->paginate(self::TICKETS_PER_PAGE)->withQueryString();

        $user = auth()->user();
        $notifications = $user?->unreadNotifications ?? [];

        return [
            'tickets' => $tickets->map(function ($ticket) {
                return $ticket->toFormattedArray();
            }),
            'categories' => $this->getCategories(),
            'tags' => $this->getTags(),
            'departments' => $this->getDepartments(),
            'users' => $this->getUsers(),
            'ticketCount' => $tickets->total(),
            'pagination' => $this->getPaginationData($tickets),
            'keyword' => $search,
            'notifications' => $notifications,
            'sort' => $sort,
            'filters' => [
                'status' => $status,
                'priority' => $priority,
                'category' => $category,
                'department' => $department,
                'assignee' => $assignee,
                'search' => $search,
                'myTickets' => false,
                'sortBy' => $sort,
            ],
        ];
    }

    /**
     * Prepare ticket detail data
     */
    public function prepareTicketData(Ticket $ticket): array
    {
        $categories = $this->getCategories();
        $tags = $this->getTags();
        $departments = $this->getDepartments();
        $users = $this->getUsers();

        $comments = $ticket->getFormattedComments();

        // Debug comments
        \Log::info('Ticket comments debug', [
            'ticket_id' => $ticket->id,
            'comments_count' => $comments->count(),
            'raw_comments' => $ticket->comments()->count(),
            'formatted_comments' => $comments->toArray()
        ]);

        $hasUpvote = auth()->check() ? $ticket->isUpvotedBy(auth()->id()) : false;

        return [
            'ticket' => [
                'id' => $ticket->id,
                'title' => $ticket->title,
                'content' => $ticket->content,
                'created_at' => $ticket->created_at->diffForHumans(),
                'updated_at' => $ticket->updated_at,
                'is_published' => $ticket->is_published,
                'user' => $ticket->user,
                'categories' => $ticket->categories,
                'tags' => $ticket->tags,
                'comments' => $comments,
                'upvote_count' => $ticket->upvotes_count,
                'has_upvote' => $hasUpvote,
                'priority' => $ticket->priority,
                'priority_name' => $ticket->priority_name,
                'priority_score' => $ticket->priority_score,
                'status' => $ticket->status,
                'status_name' => $ticket->status_name,
                'assignee' => $ticket->assignee,
                'department' => $ticket->department,
                'is_auto_assigned' => $ticket->isAutoAssigned(),
                'automation_history' => $ticket->getAutomationHistory(),
            ],
            'categories' => $categories,
            'tags' => $tags,
            'departments' => $departments,
            'users' => $users,
        ];
    }

    /**
     * Get data for ticket create page
     */
    public function getCreateTicketData(): array
    {
        return [
            'categories' => $this->getCategories(),
            'tags' => $this->getTags(),
            'departments' => $this->getDepartments(),
            'users' => $this->getUsers(),
            'notifications' => auth()->check() ? auth()->user()->unreadNotifications : [],
        ];
    }

    /**
     * Get categories for dropdowns
     */
    private function getCategories(): Collection
    {
        return Category::select(['id', 'title', 'slug'])
            ->withCount('tickets')
            ->orderBy('tickets_count', 'desc')
            ->get();
    }

    /**
     * Get tags for dropdowns
     */
    private function getTags(): Collection
    {
        return Tag::select(['id', 'name', 'slug'])
            ->withCount('tickets')
            ->orderBy('tickets_count', 'desc')
            ->get();
    }

    /**
     * Get departments for dropdowns
     */
    private function getDepartments(): Collection
    {
        return Departments::select(['id', 'name'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Get users for assignment dropdowns
     */
    private function getUsers(): Collection
    {
        return User::select(['id', 'name', 'email'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Get pagination data
     */
    private function getPaginationData(LengthAwarePaginator $paginator): array
    {
        return [
            'total' => $paginator->total(),
            'per_page' => $paginator->perPage(),
            'current_page' => $paginator->currentPage(),
            'last_page' => $paginator->lastPage(),
            'from' => $paginator->firstItem(),
            'to' => $paginator->lastItem(),
            'has_more_pages' => $paginator->hasMorePages(),
            'prev_page_url' => $paginator->previousPageUrl(),
            'next_page_url' => $paginator->nextPageUrl(),
        ];
    }

    /**
     * Get ticket statistics
     */
    public function getTicketStats(): array
    {
        return [
            'total' => Ticket::count(),
            'open' => Ticket::where('status', 'open')->count(),
            'in_progress' => Ticket::where('status', 'in_progress')->count(),
            'resolved' => Ticket::where('status', 'resolved')->count(),
            'closed' => Ticket::where('status', 'closed')->count(),
        ];
    }

    /**
     * Search tickets
     */
    public function searchTickets(Request $request): array
    {
        $search = $request->input('q', $request->input('search', ''));
        $sort = $request->input('sortBy', $request->input('sort', 'relevance'));
        $category = $request->input('category', null);
        $status = $request->input('status', null);
        $priority = $request->input('priority', null);

        // Use search functionality similar to TicketService
        $ticketIds = Ticket::search($search)->keys();

        $tickets = Ticket::whereIn('id', $ticketIds)
            ->where('is_published', true)
            ->with(['user', 'categories', 'assignee', 'department'])
            ->withCount(['upvotes', 'comments'])
            ->when($category, function ($q) use ($category) {
                $q->whereHas('categories', function ($subQ) use ($category) {
                    // Support both slug and ID for backward compatibility
                    if (is_numeric($category)) {
                        $subQ->where('categories.id', $category);
                    } else {
                        $subQ->where('categories.slug', $category);
                    }
                });
            })
            ->when($status, fn ($q) => $q->where('status', $status))
            ->when($priority, fn ($q) => $q->where('priority', $priority))
            ->when($sort === 'latest' || $sort === 'newest', fn ($q) => $q->orderByRaw("FIELD(priority, 'urgent', 'high', 'medium', 'low')")->latest())
            ->when($sort === 'oldest', fn ($q) => $q->oldest())
            ->when($sort === 'upvotes' || $sort === 'most-upvoted', fn ($q) => $q->orderBy('upvotes_count', 'desc'))
            ->when($sort === 'most-replies', fn ($q) => $q->orderBy('comments_count', 'desc'))
            ->when($sort === 'priority', fn ($q) => $q->orderByRaw("FIELD(priority, 'urgent', 'high', 'medium', 'low')"))
            ->when($sort === 'inactive', fn ($q) => $q->orderByRaw("FIELD(status, 'closed', 'resolved', 'in_progress', 'open')")->orderBy('updated_at', 'desc'))
            ->when($sort === 'relevance', function ($q) use ($search) {
                // For relevance, order by title match first, then content match
                if ($search) {
                    return $q->orderByRaw("
                        CASE
                            WHEN title LIKE ? THEN 1
                            WHEN content LIKE ? THEN 2
                            ELSE 3
                        END, created_at DESC
                    ", ["%{$search}%", "%{$search}%"]);
                }
                return $q->orderByRaw("FIELD(priority, 'urgent', 'high', 'medium', 'low')")->latest();
            })
            ->paginate(self::TICKETS_PER_PAGE)
            ->withQueryString();

        $user = auth()->user();
        $notifications = $user ? $user->unreadNotifications : [];

        return [
            'tickets' => $tickets->map(fn ($ticket) => $ticket->toFormattedArray()),
            'categories' => $this->getCategories(),
            'departments' => $this->getDepartments(),
            'users' => $this->getUsers(),
            'tags' => $this->getTags(),
            'ticketCount' => $tickets->total(),
            'pagination' => $this->getPaginationData($tickets),
            'keyword' => $search,
            'notifications' => $notifications,
            'sort' => $sort,
            'filters' => [
                'search' => $search,
                'category' => $category,
                'status' => $status,
                'priority' => $priority,
                'sortBy' => $sort,
            ],
        ];
    }

    /**
     * Store a new ticket
     */
    public function storeTicket(\App\Data\Ticket\CreateTicketData $ticketData): array
    {
        try {
            \DB::beginTransaction();

            // Generate unique slug
            $slug = $this->generateUniqueSlug($ticketData->title);

            // Create the ticket
            $ticket = Ticket::create([
                'title' => $ticketData->title,
                'content' => $ticketData->content,
                'user_id' => auth()->id(),
                'is_published' => $ticketData->is_published,
                'slug' => $slug,
                'status' => 'open',
                'priority' => 'medium',
                'categories' =>  $ticketData->categories,
                'tags' => $ticketData->tags,
            ]);

            // Attach categories
            if (!empty($ticketData->categories)) {
                $categoryIds = collect($ticketData->categories)->filter();
                if ($categoryIds->isNotEmpty()) {
                    $ticket->categories()->attach($categoryIds);
                }
            }

            if (!empty($ticketData->tags)) {
                $tagIds = collect($ticketData->tags)->filter();
                if ($tagIds->isNotEmpty()) {
                    $ticket->tags()->attach($tagIds);
                }
            }


            \DB::commit();

            return [
                'success' => true,
                'message' => 'Ticket created successfully!',
                'ticket' => $ticket->fresh(['user', 'categories', 'tags']),
            ];

        } catch (\Exception $e) {
            \DB::rollBack();
            
            \Log::error('Failed to create ticket', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'data' => $ticketData->toArray(),
            ]);

            return [
                'success' => false,
                'errors' => ['general' => 'Failed to create ticket. Please try again.'],
                'message' => 'Failed to create ticket',
            ];
        }
    }

    /**
     * Update an existing ticket
     */
    public function updateTicket(\App\Data\Ticket\CreateTicketData $ticketData, Ticket $ticket): array
    {
        try {
            \DB::beginTransaction();

            // Update ticket fields
            $ticket->update([
                'title' => $ticketData->title,
                'content' => $ticketData->content,
                'is_published' => $ticketData->is_published,
            ]);

            // Update categories
            if (!empty($ticketData->categories)) {
                $categoryIds = collect($ticketData->categories)->pluck('id')->filter();
                $ticket->categories()->sync($categoryIds);
            } else {
                $ticket->categories()->detach();
            }

            // Update tags
            if (!empty($ticketData->tags)) {
                $tagIds = collect($ticketData->tags)->pluck('id')->filter();
                $ticket->tags()->sync($tagIds);
            } else {
                $ticket->tags()->detach();
            }

            \DB::commit();

            return [
                'success' => true,
                'message' => 'Ticket updated successfully!',
                'ticket' => $ticket->fresh(['user', 'categories', 'tags']),
            ];

        } catch (\Exception $e) {
            \DB::rollBack();
            
            \Log::error('Failed to update ticket', [
                'error' => $e->getMessage(),
                'ticket_id' => $ticket->id,
                'user_id' => auth()->id(),
                'data' => $ticketData->toArray(),
            ]);

            return [
                'success' => false,
                'errors' => ['general' => 'Failed to update ticket. Please try again.'],
                'message' => 'Failed to update ticket',
            ];
        }
    }

    /**
     * Delete a ticket
     */
    public function deleteTicket(Ticket $ticket): array
    {
        try {
            $ticket->delete();

            return [
                'success' => true,
                'message' => 'Ticket deleted successfully!',
            ];

        } catch (\Exception $e) {
            \Log::error('Failed to delete ticket', [
                'error' => $e->getMessage(),
                'ticket_id' => $ticket->id,
                'user_id' => auth()->id(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to delete ticket',
            ];
        }
    }

    /**
     * Get my tickets with filtering and pagination
     */
    public function getMyTickets(Request $request): array
    {
        $search = $request->input('search', '');
        $sort = $request->input('sort', 'latest');
        $status = $request->input('status', null);
        $priority = $request->input('priority', null);

        // Build query for user's tickets
        $query = Ticket::query()
            ->where('user_id', auth()->id())
            ->with(['user', 'categories', 'tags', 'assignee', 'department'])
            ->withCount(['upvotes', 'comments']);

        // Apply search
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // Apply filters
        if ($status) {
            $query->where('status', $status);
        }

        if ($priority) {
            $query->where('priority', $priority);
        }

        // Apply sorting
        switch ($sort) {
            case 'latest':
            case 'newest':
                $query->latest();
                break;
            case 'oldest':
                $query->oldest();
                break;
            case 'priority':
                $query->orderByRaw("FIELD(priority, 'urgent', 'high', 'medium', 'low')");
                break;
            case 'upvotes':
            case 'most-upvoted':
                $query->orderBy('upvotes_count', 'desc');
                break;
            case 'most-replies':
                $query->orderBy('comments_count', 'desc');
                break;
        }

        $tickets = $query->paginate(self::TICKETS_PER_PAGE)->withQueryString();

        return [
            'tickets' => $tickets->map(function ($ticket) {
                return $ticket->toFormattedArray();
            }),
            'categories' => $this->getCategories(),
            'tags' => $this->getTags(),
            'departments' => $this->getDepartments(),
            'users' => $this->getUsers(),
            'ticketCount' => $tickets->total(),
            'pagination' => $this->getPaginationData($tickets),
            'keyword' => $search,
            'notifications' => auth()->user()->unreadNotifications ?? [],
            'sort' => $sort,
            'filters' => [
                'status' => $status,
                'priority' => $priority,
                'search' => $search,
                'myTickets' => true,
                'sortBy' => $sort,
            ],
        ];
    }

    /**
     * Generate unique slug for ticket
     */
    private function generateUniqueSlug(string $title): string
    {
        $baseSlug = \Str::slug($title);
        $slug = $baseSlug;
        $counter = 1;

        while (Ticket::where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Get ticket count
     */
    public function getTicketCount(): int
    {
        return Ticket::count();
    }

    /**
     * Get top voted tickets
     */
    public function getTopVoteTickets(int $limit = 5): array
    {
        return Ticket::withCount('upvotes')
            ->where('is_published', true)
            ->orderBy('upvotes_count', 'desc')
            ->limit($limit)
            ->get()
            ->map(fn ($ticket) => $ticket->toFormattedArray())
            ->toArray();
    }

    /**
     * Get top voted tickets (alias)
     */
    public function getTopVotedTickets(int $limit = 5): array
    {
        return $this->getTopVoteTickets($limit);
    }

    /**
     * Get edit ticket data
     */
    public function getEditTicketData(string $slug): array
    {
        try {
            $ticket = Ticket::where('slug', $slug)->firstOrFail();

            return [
                'success' => true,
                'ticket' => $ticket->toFormattedArray(),
                'categories' => $this->getCategories(),
                'tags' => $this->getTags(),
                'departments' => $this->getDepartments(),
                'users' => $this->getUsers(),
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Ticket not found',
            ];
        }
    }

    /**
     * Get tickets by category slug
     */
    public function getTicketsByCategorySlug(string $categorySlug): array
    {
        $category = Category::where('slug', $categorySlug)->firstOrFail();

        $tickets = Ticket::whereHas('categories', function ($q) use ($category) {
                $q->where('categories.id', $category->id);
            })
            ->where('is_published', true)
            ->with(['user', 'categories', 'tags', 'assignee', 'department'])
            ->withCount(['upvotes', 'comments'])
            ->orderByRaw("FIELD(priority, 'urgent', 'high', 'medium', 'low')")
            ->latest()
            ->paginate(self::TICKETS_PER_PAGE);

        return [
            'tickets' => $tickets->map(fn ($ticket) => $ticket->toFormattedArray()),
            'category' => $category,
            'categories' => $this->getCategories(),
            'tags' => $this->getTags(),
            'departments' => $this->getDepartments(),
            'users' => $this->getUsers(),
            'ticketCount' => $tickets->total(),
            'pagination' => $this->getPaginationData($tickets),
        ];
    }

    /**
     * Get tickets by tag slug
     */
    public function getTicketsByTagSlug(string $tagSlug): array
    {
        $tag = Tag::where('slug', $tagSlug)->firstOrFail();

        $tickets = Ticket::whereHas('tags', function ($q) use ($tag) {
                $q->where('tags.id', $tag->id);
            })
            ->where('is_published', true)
            ->with(['user', 'categories', 'tags', 'assignee', 'department'])
            ->withCount(['upvotes', 'comments'])
            ->orderByRaw("FIELD(priority, 'urgent', 'high', 'medium', 'low')")
            ->latest()
            ->paginate(self::TICKETS_PER_PAGE);

        return [
            'tickets' => $tickets->map(fn ($ticket) => $ticket->toFormattedArray()),
            'tag' => $tag,
            'categories' => $this->getCategories(),
            'tags' => $this->getTags(),
            'departments' => $this->getDepartments(),
            'users' => $this->getUsers(),
            'ticketCount' => $tickets->total(),
            'pagination' => $this->getPaginationData($tickets),
        ];
    }

    /**
     * Store transferred ticket from external system
     */
    public function storeTransferredTicket(array $data): array
    {
        try {
            \DB::beginTransaction();

            // Generate unique slug
            $slug = $this->generateUniqueSlug($data['title']);

            // Create the ticket
            $ticket = Ticket::create([
                'title' => $data['title'],
                'content' => $data['content'],
                'user_id' => $data['user_id'],
                'is_published' => true,
                'slug' => $slug,
                'status' => 'open',
                'priority' => $data['priority'] ?? 'medium',
                'category_type' => 'general',
            ]);

            // Attach categories if provided
            if (!empty($data['categories'])) {
                $categoryIds = collect($data['categories'])->filter();
                if ($categoryIds->isNotEmpty()) {
                    $ticket->categories()->attach($categoryIds);
                }
            }

            // Attach tags if provided
            if (!empty($data['tags'])) {
                $tagIds = collect($data['tags'])->filter();
                if ($tagIds->isNotEmpty()) {
                    $ticket->tags()->attach($tagIds);
                }
            }

            \DB::commit();

            return [
                'success' => true,
                'message' => 'Ticket transferred successfully!',
                'ticket' => $ticket->fresh(['user', 'categories', 'tags']),
            ];

        } catch (\Exception $e) {
            \DB::rollBack();
            
            \Log::error('Failed to transfer ticket', [
                'error' => $e->getMessage(),
                'data' => $data,
            ]);

            return [
                'success' => false,
                'errors' => ['general' => 'Failed to transfer ticket. Please try again.'],
                'message' => 'Failed to transfer ticket',
            ];
        }
    }

    /**
     * Undo delete ticket
     */
    public function undoDeleteTicket(string $ticketId): array
    {
        try {
            $ticket = Ticket::withTrashed()->findOrFail($ticketId);
            
            if ($ticket->trashed()) {
                $ticket->restore();
                
                return [
                    'success' => true,
                    'message' => 'Ticket restored successfully!',
                ];
            }

            return [
                'success' => false,
                'message' => 'Ticket is not deleted',
            ];

        } catch (\Exception $e) {
            \Log::error('Failed to restore ticket', [
                'error' => $e->getMessage(),
                'ticket_id' => $ticketId,
            ]);

            return [
                'success' => false,
                'message' => 'Failed to restore ticket',
            ];
        }
    }
}
