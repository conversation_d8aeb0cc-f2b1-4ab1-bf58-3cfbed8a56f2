<?php

use App\Events\NewQuestionCreated;
use App\Http\Controllers\Admin\AdminController;
use Inertia\Inertia;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\Comment\CommentsController;
use App\Http\Controllers\Department\DepartmentController;
use App\Http\Controllers\DocsController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\Oauth\SocialAuthController;
use App\Http\Controllers\Permission\PermissionController;
use App\Http\Controllers\Ticket\TicketController;
use App\Http\Controllers\Role\RoleController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\UpvoteController;
use App\Http\Controllers\UserController;    
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PostController;
// Homepage
// Route::get('/', [TicketController::class, 'index'])->name('/');
Route::get('/', [TicketController::class, 'index'])->name('home');
// Ticket routes
// Route::get('/tickets', [TicketController::class, 'index'])->name('tickets.index');
// Route::get('/all', [TicketController::class, 'getAllTicket'])->name('all');
// Route::get('/mytickets' , TicketController::class, 'getMyTickets')->name('mytickets');
Route::get('/top-voted-tickets', [TicketController::class, 'getTopVoteTickets']);


// API endpoint for search suggestions
Route::get('/api/search/suggestions', [TicketController::class, 'apiSearchSuggestions'])
    ->name('api.search.suggestions');
Route::get('/admin/tickets/trash', [TicketController::class, 'getTrash'])->name('tickets.trash');
// Route::get('/tickets', [TicketController::class, 'getTicketByUser']);
Route::middleware(['auth'])->group(function () {
    Route::get('/tickets/create', [PostController::class, 'create'])->name('tickets.create');
    Route::post('/tickets', [PostController::class, 'store'])->name('tickets.store');
    Route::get('/tickets/{slug}/edit', [TicketController::class, 'edit'])->name('tickets.edit');
    Route::put('/tickets/{ticket}', [TicketController::class, 'update'])->name('tickets.update');
    Route::delete('/tickets/{ticket}/delete', [TicketController::class, 'destroy'])->name('tickets.destroy');
    Route::post('/tickets/{id}/restore', [TicketController::class, 'restore'])->name('tickets.restore');
    Route::post('/comments', [CommentsController::class, 'store'])
        ->middleware('comment.rate.limit')
        ->name('comments.store');

    // Nếu bạn muốn thêm routes cho reply comments
    Route::post('/comments/{comment}/reply', [CommentsController::class, 'reply'])->name('comments.reply');

});
Route::get('/tickets/search', [TicketController::class, 'search'])->name('tickets.search');
Route::get('/categories/{categorySlug}/tickets', [TicketController::class, 'filterTicketByCategory'])
    ->name('categories.tickets.index');

Route::get('/tags/{tagsSlug}/tickets', [TicketController::class, 'filterTicketByTag'])
    ->name('tags.tickets.index');

Route::get('/tickets/{slug}', [TicketController::class, 'show'])->name('tickets.show');

Route::post('/tickets/{id}/upvote', [UpvoteController::class, 'upvote'])
    ->name('tickets.upvote');
Route::patch('/tickets/{ticket}/update-status', [TicketController::class, 'updateStatus'])
    ->name('tickets.update-status');
Route::delete('/comments/{comment}', [CommentsController::class, 'destroy'])
    ->middleware(['auth:sanctum'])
    ->name('comments.destroy');

Route::get('/categories', [CategoryController::class, 'index'])->name('categories');
Route::post('/notifications/read-all', [NotificationController::class, 'markAllAsRead'])
    ->middleware('auth')
    ->name('notifications.read_all');
Route::get('/send-email', [\Illuminate\Notifications\Notification::class, 'sendEmailNotification']);

Route::middleware(['auth:sanctum', 'verified'])->group(function () {
//     Route::get('/admin/docs/{file?}', [DocsController::class, 'show'])
//         ->where('file', '.*\.md')
//         ->name('admin.docs.show');

//     Route::get('/dashboard', function () {
//         return view('dashboard');
//     })->name('dashboard');
// });



    // Test assign permissions - DETAILED DEBUG
    Route::get('/test-assign', function() {
        $user = auth()->user();

        // Debug chi tiết
        $allPermissions = \Spatie\Permission\Models\Permission::all()->pluck('name');
        $userPermissions = $user->getAllPermissions()->pluck('name');
        $userRoles = $user->roles;

        // Check specific permission
        $assignPostsPermission = \Spatie\Permission\Models\Permission::where('name', 'assign-posts')->first();
        $adminRole = \Spatie\Permission\Models\Role::where('name', 'Admin')->first();

        return [
            'user_info' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
            ],
            'roles' => $userRoles->map(function($role) {
                return [
                    'name' => $role->name,
                    'permissions' => $role->permissions->pluck('name')
                ];
            }),
            'user_permissions' => $userPermissions,
            'permission_checks' => [
                'assign-posts' => $user->can('assign-posts'),
                'access-admin-dashboard' => $user->can('access-admin-dashboard'),
                'view-any-posts' => $user->can('view-any-posts'),
                'manage-users' => $user->can('manage-users'),
            ],
            'permission_details' => [
                'assign_posts_exists' => $assignPostsPermission ? true : false,
                'admin_role_exists' => $adminRole ? true : false,
                'admin_role_has_assign_posts' => $adminRole ? $adminRole->hasPermissionTo('assign-posts') : false,
                'user_has_admin_role' => $user->hasRole('Admin'),
                'user_direct_permissions' => $user->getDirectPermissions()->pluck('name'),
                'user_role_permissions' => $user->getPermissionsViaRoles()->pluck('name'),
            ],
            'all_permissions_in_system' => $allPermissions,
            'debug_info' => [
                'cache_cleared' => 'Run: php artisan cache:clear',
                'permission_cache_cleared' => 'Run: app()[\\Spatie\\Permission\\PermissionRegistrar::class]->forgetCachedPermissions()',
                'suggestion' => 'Check if assign-posts permission exists and Admin role has it'
            ]
        ];
    });

    // Admin routes với middleware admin.access
    Route::middleware(['auth'])->group(function () {
        Route::get('/admin', [AdminController::class, 'dashboard'])->name('admin.dashboard');
        Route::get('/admin/posts', [AdminController::class, 'getAllTicket'])->name('admin.posts');
        Route::get('/admin/categories', [AdminController::class, 'getAllCategory'])->name('admin.categories');
        Route::post('/admin/create-category', [CategoryController::class, 'store'])->name('admin.categories.store');
        Route::put('/admin/categories/{category}', [CategoryController::class, 'update'])->name('admin.categories.update');
        Route::delete('/admin/categories/{category}', [CategoryController::class, 'destroy'])->name('admin.categories.destroy');
        Route::delete('/admin/categories/{category}/remove-logo', [CategoryController::class, 'removeLogo'])->name('admin.categories.remove-logo');
        Route::get('/admin/tags', [AdminController::class, 'getAllTags'])->name('admin.tags');
    });

    // Role & Permission management routes với middleware đặc biệt
    Route::middleware(['role.permission.access'])->group(function () {
        Route::get('/admin/roles-permissions', [AdminController::class, 'getAllRolesAndPermissions'])->name('admin.roles-permissions');
        Route::get('/permissions', [PermissionController::class, 'index'])->name('permissions.index');
        Route::post('/users/assign-permissions', [PermissionController::class, 'assignPermissions'])->name('users.assignPermissions');
        Route::get('/roles', [RoleController::class, 'index'])->name('roles.index');
        Route::post('/users/assign-role', [RoleController::class, 'assignRole'])->name('users.assign-role');
    });

    // Automation Rules routes
    Route::resource('/admin/automation-rules', \App\Http\Controllers\Admin\AutomationRuleController::class)->names([
        'index' => 'admin.automation-rules.index',
        'create' => 'admin.automation-rules.create',
        'store' => 'admin.automation-rules.store',
        'show' => 'admin.automation-rules.show',
        'edit' => 'admin.automation-rules.edit',
        'update' => 'admin.automation-rules.update',
        'destroy' => 'admin.automation-rules.destroy',
    ]);

    Route::patch('/admin/automation-rules/{automationRule}/toggle', [\App\Http\Controllers\Admin\AutomationRuleController::class, 'toggleActive'])->name('admin.automation-rules.toggle');
    Route::post('/admin/automation-rules/{automationRule}/test', [\App\Http\Controllers\Admin\AutomationRuleController::class, 'test'])->name('admin.automation-rules.test');
    Route::get('/admin/automation-stats', [\App\Http\Controllers\Admin\AutomationRuleController::class, 'stats'])->name('admin.automation-rules.stats');
    Route::post('/admin/bulk-update-scores', [\App\Http\Controllers\Admin\AutomationRuleController::class, 'bulkUpdateScores'])->name('admin.bulk-update-scores');

    // Ticket bulk operations routes
    Route::post('/admin/tickets/assign', [\App\Http\Controllers\Admin\TicketBulkController::class, 'assign'])->name('admin.tickets.assign');
    Route::post('/admin/tickets/bulk-assign', [\App\Http\Controllers\Admin\TicketBulkController::class, 'bulkAssign'])->name('admin.tickets.bulk-assign');
    Route::post('/admin/tickets/bulk-status', [\App\Http\Controllers\Admin\TicketBulkController::class, 'bulkStatus'])->name('admin.tickets.bulk-status');
    Route::post('/admin/tickets/bulk-priority', [\App\Http\Controllers\Admin\TicketBulkController::class, 'bulkPriority'])->name('admin.tickets.bulk-priority');
    Route::post('/admin/tickets/bulk-department', [\App\Http\Controllers\Admin\TicketBulkController::class, 'bulkDepartment'])->name('admin.tickets.bulk-department');
    Route::post('/admin/tickets/bulk-close-resolved', [\App\Http\Controllers\Admin\TicketBulkController::class, 'bulkCloseResolved'])->name('admin.tickets.bulk-close-resolved');
    Route::post('/admin/tickets/bulk-add-tags', [\App\Http\Controllers\Admin\TicketBulkController::class, 'bulkAddTags'])->name('admin.tickets.bulk-add-tags');
    Route::post('/admin/tickets/bulk-duplicate', [\App\Http\Controllers\Admin\TicketBulkController::class, 'bulkDuplicate'])->name('admin.tickets.bulk-duplicate');
    Route::post('/admin/tickets/bulk-archive', [\App\Http\Controllers\Admin\TicketBulkController::class, 'bulkArchive'])->name('admin.tickets.bulk-archive');
    Route::post('/admin/tickets/bulk-delete', [\App\Http\Controllers\Admin\TicketBulkController::class, 'bulkDelete'])->name('admin.tickets.bulk-delete');
    Route::get('/admin/tickets/{slug}', [\App\Http\Controllers\Admin\TicketBulkController::class, 'show'])->name('admin.tickets.show');
    Route::get('/admin/tickets/{slug}/comments', [\App\Http\Controllers\Admin\TicketBulkController::class, 'getComments'])->name('admin.tickets.comments');
    Route::post('/admin/tickets/{slug}/respond', [\App\Http\Controllers\Admin\TicketBulkController::class, 'addResponse'])->name('admin.tickets.respond');
    Route::post('/admin/tickets/{id}/status', [\App\Http\Controllers\Admin\TicketBulkController::class, 'updateStatus'])->name('admin.tickets.update-status');
    // Documentation routes
    Route::get('/admin/docs', [DocsController::class, 'adminIndex'])->name('admin.docs.index');
    Route::get('/admin/docs/{file?}', [DocsController::class, 'show'])->name('admin.docs.show');

});
// Route::post('/notifications/{id}/read', [NotificationController::class, 'markAsRead']);
// Route::post('/notifications/read-all', [NotificationController::class, 'markAllAsRead']);
Route::delete('/notifications/{id}', [NotificationController::class, 'destroy'])->name('notifications.destroy');

// Route::get('/test-event', function () {
//    $post = Post::find('01jp1xepa4cv3en4axatkh9vdk');
//    event(new NewQuestionCreated($post));
//
//    return 'Event dispatched!';
// });

Route::post('/notifications/{id}/read', [NotificationController::class, 'markAsRead'])->name('notifications.read_all');
Route::post('/notifications/mark-all-as-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.markAllAsRead');
Route::resource('/admin/tags', TagController::class)->only(['store', 'update', 'destroy']);

Route::get('/user/profile', [\App\Http\Controllers\UserController::class, 'show'])
    ->name('profile.show')
    ->middleware(['auth', 'verified']);

Route::get('/auth/google', [SocialAuthController::class, 'redirectToGoogle'])->name('auth.google');
Route::get('/auth/google/callback', [SocialAuthController::class, 'handleGoogleCallback']);

Route::get('/auth/github', [SocialAuthController::class, 'redirectToGithub'])->name('auth.github');
Route::get('/auth/github/callback', [SocialAuthController::class, 'handleGithubCallback']);

// Route::get('auth/{provider}', [SocialAuthController::class, 'redirectToProvider'])
//    ->name('auth.{provider}');
// Route::get('auth/{provider}/callback', [SocialAuthController::class, 'handleProviderCallback'])
//    ->name('auth.{provider}.callback');
Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications.index');

Route::get('/preview-email', function () {
    return view('mail.notification');
});
Route::get('/form', [TicketController::class, 'showForm']);


// Route::get('/tickets/spa', [\App\Http\Controllers\Ticket\TicketController::class, 'manager'])->name('tickets.spa');
Route::get('/tickets/my-tickets', [TicketController::class, 'getMyTickets'])->name('tickets.my')->middleware('auth');
Route::get('/tickets/search', [TicketController::class, 'search'])->name('tickets.search');
Route::get('/tickets/filter', [TicketController::class, 'index'])->name('tickets.filter');
Route::get('/tickets/{slug}', [TicketController::class, 'show'])->name('tickets.show');

// AJAX API routes for ticket data
// Route::middleware(['web'])->group(function () {
//     Route::get('/api/tickets/data', [\App\Http\Controllers\Ticket\TicketController::class, 'getTicketsData'])->name('tickets.data');
//     Route::get('/api/tickets/my/data', [\App\Http\Controllers\PostController::class, 'getMyTicketsData'])->name('tickets.my.data')->middleware('auth');
// });

Route::get('/admin/users', [UserController::class, 'index'])->name('users.index');

Route::middleware(['auth'])->group(function () {
    Route::get('/departments/{slug}', [DepartmentController::class, 'show'])->name('departments.show');
    Route::get('/departments/{slug}/employee', [DepartmentController::class, 'getEmployee'])->name('departments.employees');
    Route::get('/departments/{department}/available-users', [DepartmentController::class, 'getAvailableUsers'])->name('users.available');
    Route::delete('/departments/{department}/users/{user}', [DepartmentController::class, 'removeUser'])->name('departments.removeUser');
    Route::post('/departments/{department}/add-user', [DepartmentController::class, 'addUser'])->name('departments.addUser');
    Route::resource('departments', DepartmentController::class)->names('departments');
});
// Route::get('/posts/{id}/showById', [PostController::class, 'showById'])->name('posts.showById');

// Role routes

// API endpoints cho role và permission - Grouped under middleware
Route::middleware(['auth:sanctum', 'verified'])->group(function () {
    Route::post('/users/assign-role-api', [PermissionController::class, 'assignRole'])->name('users.assign-role-api');
    Route::post('/users/assign-permissions-api', [PermissionController::class, 'assignPermissions'])->name('users.assign-permissions-api');
    Route::post('/admin/roles', [RoleController::class, 'storeRole'])->name('admin.roles.store');
    Route::put('/admin/roles/{id}', [RoleController::class, 'updateRole'])->name('admin.roles.update');

    // API endpoints for permissions
    Route::post('/admin/permissions', [PermissionController::class, 'store'])->name('admin.permissions.store');
    Route::put('/admin/permissions/{id}', [PermissionController::class, 'update'])->name('admin.permissions.update');
    Route::delete('/admin/permissions/{id}', [PermissionController::class, 'destroy'])->name('admin.permissions.destroy');
});

// Admin routes
Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {

    Route::get('/notifications', [AdminController::class, 'notifications'])->name('notifications');

    Route::get('/tickets/{id}', [AdminController::class, 'getTicket'])->name('tickets.get');
    Route::get('/assignment-data', [AdminController::class, 'getAssignmentData'])->name('assignment-data');
    Route::get('/report', [AdminController::class, 'getReport'])->name('report');

    // API Management Dashboard
    Route::get('/api-dashboard', [\App\Http\Controllers\Admin\ApiDashboardController::class, 'index'])->name('api-dashboard');

    // Test page for API management features
    Route::get('/test-api-pages', function () {
        return inertia('Admin/TestApiPages');
    })->name('test-api-pages');

    // Test route for debugging
    Route::get('/test-debug', function () {
        return response()->json([
            'message' => 'API routes are working',
            'timestamp' => now(),
            'user' => auth()->user() ? auth()->user()->name : 'Guest'
        ]);
    })->name('test-debug');

    // Test comment creation and retrieval
    Route::get('/test-comments/{ticketSlug?}', function ($ticketSlug = null) {
        try {
            // Get first ticket if no slug provided
            if (!$ticketSlug) {
                $ticket = \App\Models\Ticket::first();
                if (!$ticket) {
                    return response()->json(['error' => 'No tickets found']);
                }
            } else {
                $ticket = \App\Models\Ticket::where('slug', $ticketSlug)->first();
                if (!$ticket) {
                    return response()->json(['error' => 'Ticket not found']);
                }
            }

            // Create a test comment
            $comment = \App\Models\Comments::create([
                'ticket_id' => $ticket->id,
                'user_id' => auth()->id() ?: 1,
                'comment' => 'Test comment created at ' . now(),
                'is_hr_response' => false,
            ]);

            // Get all comments for this ticket
            $comments = $ticket->getFormattedComments();

            return response()->json([
                'success' => true,
                'ticket_id' => $ticket->id,
                'ticket_slug' => $ticket->slug,
                'created_comment' => $comment,
                'all_comments' => $comments,
                'comments_count' => $comments->count(),
                'raw_comments_count' => $ticket->comments()->count(),
                'ticket_url' => route('tickets.show', $ticket->slug)
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()], 500);
        }
    })->name('test-comments');

    // Test Transfer API
    Route::get('/test-transfer-api', function () {
        $apiKey = config('app.transfer_api_key');

        if (!$apiKey) {
            return response()->json([
                'error' => 'TRANSFER_API_KEY not configured in .env file',
                'instruction' => 'Add TRANSFER_API_KEY=your-secret-key to .env file'
            ]);
        }

        // Test data
        $testData = [
            'title' => 'Test Transfer Ticket ' . time(),
            'content' => 'This is a test ticket transferred from external product at ' . now(),
            'category' => 'Test Product',
            'email' => 'test' . time() . '@example.com',
            'username' => 'Test User ' . time()
        ];

        return response()->json([
            'api_key_configured' => true,
            'api_key_length' => strlen($apiKey),
            'test_data' => $testData,
            'endpoints' => [
                'transfer' => url('/api/transfer/ticket'),
                'status' => url('/api/transfer/status')
            ],
            'curl_example' => "curl -X POST " . url('/api/transfer/ticket') . " \\\n" .
                             "  -H \"Content-Type: application/json\" \\\n" .
                             "  -H \"X-API-Key: {$apiKey}\" \\\n" .
                             "  -d '" . json_encode($testData, JSON_PRETTY_PRINT) . "'"
        ]);
    })->name('test-transfer-api');

    // Test system user creation
    Route::get('/test-system-user', function (\Illuminate\Http\Request $request) {
        try {
            // Check if columns exist
            $columns = \Illuminate\Support\Facades\DB::select("SHOW COLUMNS FROM users LIKE 'is_system_user'");
            if (empty($columns)) {
                return response()->json(['error' => 'is_system_user column does not exist']);
            }

            $user = \App\Models\User::create([
                'name' => 'Test System User ' . time(),
                'email' => 'test' . time() . '@system.com',
                'password' => \Illuminate\Support\Facades\Hash::make('password'),
                'is_system_user' => true,
                'system_type' => 'test',
                'rate_limit_tier' => 'standard',
                'is_active' => true,
            ]);

            return response()->json(['success' => true, 'user' => $user]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()], 500);
        }
    })->name('test-system-user');

    // Test ticket creation
    Route::post('/test-ticket-create', function (\Illuminate\Http\Request $request) {
        try {
            $ticket = \App\Models\Ticket::create([
                'title' => 'Test Ticket ' . time(),
                'content' => 'This is a test ticket content',
                'priority' => 'medium',
                'department_id' => 1,
                'user_id' => auth()->id() ?: 1,
                'slug' => 'test-ticket-' . time(),
                'status' => 'open',
                'is_published' => true,
            ]);

            return response()->json(['success' => true, 'ticket' => $ticket]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()], 500);
        }
    })->name('test-ticket-create');

    // System User Management
    Route::resource('system-users', \App\Http\Controllers\Admin\SystemUserController::class);
    Route::post('/system-users/{user}/toggle-status', [\App\Http\Controllers\Admin\SystemUserController::class, 'toggleStatus'])->name('system-users.toggle-status');
    Route::get('/system-users-stats', [\App\Http\Controllers\Admin\SystemUserController::class, 'getStats'])->name('system-users.stats');

    // Token Management
    Route::resource('tokens', \App\Http\Controllers\Admin\TokenManagementController::class);
    Route::post('/tokens/{token}/rotate', [\App\Http\Controllers\Admin\TokenManagementController::class, 'rotate'])->name('tokens.rotate');
    Route::get('/system-users-list', [\App\Http\Controllers\Admin\TokenManagementController::class, 'getSystemUsers'])->name('tokens.system-users');

    // Audit Logs
    Route::get('/audit/api-logs', [\App\Http\Controllers\Admin\AuditLogController::class, 'apiLogs'])->name('audit.api-logs');
    Route::get('/audit/token-logs', [\App\Http\Controllers\Admin\AuditLogController::class, 'tokenLogs'])->name('audit.token-logs');
    Route::get('/audit/security-alerts', [\App\Http\Controllers\Admin\AuditLogController::class, 'securityAlerts'])->name('audit.security-alerts');
    Route::get('/audit/api-logs/export', [\App\Http\Controllers\Admin\AuditLogController::class, 'exportApiLogs'])->name('audit.api-logs.export');
    Route::get('/audit/api-stats', [\App\Http\Controllers\Admin\AuditLogController::class, 'getApiStats'])->name('audit.api-stats');
    Route::get('/audit/monitoring', [\App\Http\Controllers\Admin\AuditLogController::class, 'getMonitoringData'])->name('audit.monitoring');
});

